"use client";

import { Button1 } from "@/app/_components/shared/Button1";
import { Button } from "@relume_io/relume-ui";
import React, { useEffect, useRef, useState } from "react";

export function Header114() {
  const sectionRef = useRef(null);
  const [isFixed, setIsFixed] = useState(true);
  const [backgroundTop, setBackgroundTop] = useState(0);
  const [parallaxOffset, setParallaxOffset] = useState(0);
  const [finalParallaxOffset, setFinalParallaxOffset] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current) {
        const sectionRect = sectionRef.current.getBoundingClientRect();
        const sectionBottom = sectionRect.bottom;
        const viewportHeight = window.innerHeight;
        const scrollY = window.scrollY;

        // Calculate parallax offset (subtle movement - 20% of scroll speed)
        const sectionTop = sectionRect.top + scrollY;
        const scrollProgress = Math.max(0, scrollY - sectionTop);
        const parallaxSpeed = 0.2; // Adjust this value for more/less parallax effect
        const currentParallaxOffset = scrollProgress * parallaxSpeed;
        setParallaxOffset(currentParallaxOffset);

        // When the section bottom reaches the bottom of the viewport, disable fixed background
        if (sectionBottom <= viewportHeight) {
          if (isFixed) {
            // Store the final parallax offset when transitioning from fixed to absolute
            setFinalParallaxOffset(currentParallaxOffset);
          }
          setIsFixed(false);
          // Calculate where the background should be positioned to maintain continuity
          const sectionHeight = sectionRef.current.offsetHeight;
          setBackgroundTop(sectionTop + sectionHeight - viewportHeight);
        } else {
          setIsFixed(true);
          setBackgroundTop(0);
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isFixed]);

  return (
    <section ref={sectionRef} id="relume" className="relative min-h-[200vh]">
      {/* Sticky background image that stays fixed while content scrolls over it */}
      <div
        className={`${isFixed ? 'fixed' : 'absolute'} h-screen w-full overflow-hidden -z-50`}
        style={{
          top: isFixed ? 0 : `${backgroundTop}px`,
          backgroundImage: "url('/images/forestforward/homepage/4.png')",
          backgroundAttachment: "fixed",
          backgroundSize: "110%", // Slightly larger to allow for parallax movement
          backgroundPosition: `center ${isFixed ? -parallaxOffset : -finalParallaxOffset}px`,
          backgroundRepeat: "no-repeat",
          transition: 'none' // Remove transition to prevent glitches
        }}
      >
        <div className="absolute inset-0 bg-black/75" />
      </div>

      {/* Content that scrolls over the sticky background */}
      <div className="absolute inset-0 z-50 px-[5%]">
        <div className="container min-h-[200vh] flex flex-col justify-between">
          {/* First content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-4xl pl-[5%] pr-[20%]">
              <h1 className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl mb-6 md:mb-8">
                We helpen je duurzaamheid uit<br></br>de directiekamer te halen.
              </h1>
              <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
                <Button1
                  title="Button"
                  variant="filled"
                >
                  Ontdek meer
                </Button1>
                <Button1
                  title="Button"
                  href={"/contact"}
                  variant="transparent"
                >
                  Contacteer ons
                </Button1>
              </div>
            </div>
          </div>

          {/* Second content section */}
          <div className="flex flex-col justify-center min-h-screen py-20">
            <div className="w-full max-w-2xl pl-[40%] pr-[5%]">
              <h1 className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6">
                Samen maken we<br></br> duurzaamheid tastbaar.
              </h1>
              <p className="text-text-alternative md:text-md ">
                Wil je meer doen dan enkel over je duurzame plannen praten? Heb je er nood aan om je interne duurzame inspanningen zichtbaarder te maken? Meer bevattelijk te maken? Dichter bij je mensen te brengen?<br></br><br></br>

Samen met je collega’s of andere stakeholders rollen we de mouwen op voor meer groen. We helpen je doordacht en dichtbij natuur te creëren die de biodiversiteit versterkt, een positieve impact heeft op de buurt en perfect aansluit bij jouw bedrijfswaarden.<br></br><br></br>

Zo groeien we samen naar een biodiversere, duurzame toekomst.
              </p>
              <Button1
                  title="Button"
                  variant="transparent"
                  className="mt-6"
                >
                  Contacteer ons
                </Button1>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}